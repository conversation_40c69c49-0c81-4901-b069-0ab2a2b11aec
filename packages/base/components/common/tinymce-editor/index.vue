<script setup lang="ts">
import Editor from '@tinymce/tinymce-vue'

defineOptions({
  name: 'TinymceEditor',
})
</script>

<template>
  <div>
    <Editor
      id="uuid"
      api-key="e139kbcylxz363gik5rfmczeg0g1ac1p7ixoaa044qer6y3g"
      :init="{
        plugins: 'advlist anchor autolink charmap code fullscreen help image insertdatetime link lists media preview searchreplace table visualblocks wordcount',
        toolbar: 'undo redo | styles | bold italic underline strikethrough | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link image',
        height: 500,
      }"
    />
  </div>
</template>

<style scoped></style>
