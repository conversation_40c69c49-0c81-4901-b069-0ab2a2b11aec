{"name": "soybean-admin", "type": "module", "version": "1.5.0", "packageManager": "pnpm@10.13.1", "description": "", "engines": {"node": ">=18.12.0", "pnpm": ">=8.7.0"}, "scripts": {"dev": "pnpm --filter @apps/admin dev", "dev:admin": "pnpm --filter @apps/admin dev", "dev:user": "pnpm --filter @apps/user dev", "build": "turbo run build", "build:admin": "turbo run build --filter @apps/admin", "build:user": "turbo run build --filter @apps/user", "build:test": "turbo run build:test", "build:test:admin": "turbo run build:test --filter @apps/admin", "build:test:user": "turbo run build:test --filter @apps/user", "preview:admin": "pnpm --filter @apps/admin preview", "preview:user": "pnpm --filter @apps/user preview", "typecheck": "turbo run typecheck", "lint:fix": "pnpm --recursive lint:fix", "stub": "pnpm -r run stub --if-present", "prepare": "husky", "commitlint": "commitlint --edit", "preinstall": "npx only-allow pnpm", "release": "commit-and-tag-version", "commit": "cz", "openapi2ts": "openapi2ts", "postinstall": "pnpm run stub"}, "dependencies": {"@better-scroll/core": "2.5.1", "@iconify/vue": "4.3.0", "@microsoft/fetch-event-source": "^2.0.1", "@milkdown/crepe": "^7.15.1", "@milkdown/kit": "^7.15.1", "@sa/axios": "workspace:*", "@sa/color": "workspace:*", "@sa/directives": "workspace:*", "@sa/hooks": "workspace:*", "@sa/materials": "workspace:*", "@sa/plugins": "workspace:^", "@sa/uno-preset": "workspace:*", "@sa/utils": "workspace:*", "@tinymce/tinymce-vue": "^6.3.0", "@vueuse/core": "12.5.0", "@yw/fabric": "workspace:*", "axios": "1.7.9", "clipboard": "2.0.11", "cropperjs": "^2.0.0", "dayjs": "1.11.13", "defu": "6.1.4", "echarts": "5.6.0", "esdk-obs-browserjs": "^3.25.6", "json5": "2.2.3", "naive-ui": "2.41.0", "nprogress": "0.2.0", "pinia": "3.0.0", "tailwind-merge": "3.0.1", "vue": "3.5.13", "vue-draggable-plus": "0.6.0", "vue-element-plus-x": "^1.3.0", "vue-router": "4.5.0", "xlsx": "^0.18.5"}, "devDependencies": {"@antfu/eslint-config": "^2.27.3", "@commitlint/cli": "^19.8.0", "@commitlint/config-conventional": "^19.8.0", "@elegant-router/vue": "0.3.8", "@iconify/json": "2.2.305", "@sa/tsconfig": "workspace:*", "@sa/uno-config": "workspace:*", "@sa/vite-config": "workspace:*", "@types/node": "22.13.1", "@types/nprogress": "0.2.3", "@umijs/openapi": "^1.13.0", "@unocss/core": "^66.3.3", "@unocss/eslint-config": "65.4.3", "@unocss/preset-icons": "65.4.3", "@unocss/preset-uno": "65.4.3", "@unocss/transformer-directives": "65.4.3", "@unocss/transformer-variant-group": "65.4.3", "@unocss/vite": "65.4.3", "@vitejs/plugin-vue": "5.2.1", "@vitejs/plugin-vue-jsx": "4.1.1", "commit-and-tag-version": "^12.5.0", "commitizen": "^4.3.1", "cz-customizable": "^7.4.0", "eslint": "9.12.0", "husky": "^9.1.7", "lint-staged": "15.4.3", "only-allow": "^1.2.1", "rollup-plugin-visualizer": "^5.14.0", "sass": "1.85.0", "tsx": "4.19.2", "turbo": "^2.5.5", "typescript": "5.8.3", "unbuild": "^3.5.0", "unplugin-auto-import": "^19.1.1", "unplugin-icons": "22.0.0", "unplugin-vue-components": "28.0.0", "vconsole": "^3.15.1", "vite": "6.1.0", "vite-plugin-checker": "^0.9.3", "vite-plugin-progress": "0.0.7", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-devtools": "7.7.1", "vue-tsc": "^2.2.12"}, "config": {"commitizen": {"path": "node_modules/cz-customizable"}, "cz-customizable": {"config": ".cz-config.cjs"}}, "pnpm": {"patchedDependencies": {"vue-element-plus-x@1.3.0": "patches/<EMAIL>"}}, "lint-staged": {"*.{js,jsx,vue,ts,tsx}": ["pnpm lint:fix"]}, "volta": {"node": "22.15.0"}}